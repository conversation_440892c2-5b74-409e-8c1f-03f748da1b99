using SilvrBear_Amazon_Automation.Models;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Interface for Amazon Seller API operations
/// </summary>
public interface IAmazonSellerApiService
{
    /// <summary>
    /// Creates an inbound shipment plan
    /// </summary>
    /// <param name="items">Items to include in the shipment</param>
    /// <param name="shipFromAddress">Address shipping from</param>
    /// <param name="labelPrepPreference">Label preparation preference</param>
    /// <returns>Inbound shipment plan response</returns>
    Task<AmazonApiResponse<InboundShipmentPlanResponse>> CreateInboundShipmentPlanAsync(
        List<ShipmentItem> items,
        Address shipFromAddress,
        string labelPrepPreference = "SELLER_LABEL");

    /// <summary>
    /// Creates an inbound shipment plan with destination fulfillment center for India marketplace
    /// </summary>
    /// <param name="items">Items to include in the shipment</param>
    /// <param name="shipFromAddress">Address shipping from</param>
    /// <param name="destinationFulfillmentCenterId">Destination fulfillment center ID for India</param>
    /// <param name="labelPrepPreference">Label preparation preference</param>
    /// <returns>Inbound shipment plan response</returns>
    Task<AmazonApiResponse<InboundShipmentPlanResponse>> CreateInboundShipmentPlanAsync(
        List<ShipmentItem> items,
        Address shipFromAddress,
        string destinationFulfillmentCenterId,
        string labelPrepPreference = "SELLER_LABEL");

    /// <summary>
    /// Creates an inbound shipment using v2024-03-20 API
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID from the plan creation</param>
    /// <param name="request">Shipment creation request</param>
    /// <returns>Create shipment response</returns>
    Task<AmazonApiResponse<CreateInboundShipmentResponse>> CreateInboundShipmentAsync(
        string inboundPlanId,
        CreateInboundShipmentRequest request);

    /// <summary>
    /// Creates an inbound shipment using v2024-03-20 API - India Marketplace Compliant
    /// Follows Amazon's documented workflow: Generate Placement → List Placement → Set Packing → Confirm Placement
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID from the plan creation</param>
    /// <param name="request">Shipment creation request</param>
    /// <returns>Create shipment response</returns>
    Task<AmazonApiResponse<CreateInboundShipmentResponse>> CreateInboundShipmentForIndiaAsync(
        string inboundPlanId,
        CreateInboundShipmentRequest request);

    /// <summary>
    /// Updates inbound shipment packing information using v2024-03-20 API
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <param name="request">Shipment update request</param>
    /// <returns>Update response</returns>
    Task<AmazonApiResponse<object>> UpdateInboundShipmentAsync(
        string inboundPlanId,
        CreateInboundShipmentRequest request);

    /// <summary>
    /// Gets inbound shipment details (preserved from v0 API)
    /// </summary>
    /// <param name="shipmentId">Shipment ID</param>
    /// <returns>Shipment details</returns>
    Task<AmazonApiResponse<GetShipmentResponse>> GetInboundShipmentAsync(string shipmentId);

    /// <summary>
    /// Gets list of inbound shipments (preserved from v0 API)
    /// </summary>
    /// <param name="shipmentStatusList">Filter by status</param>
    /// <param name="shipmentIdList">Filter by shipment IDs</param>
    /// <param name="lastUpdatedAfter">Filter by last updated date</param>
    /// <param name="lastUpdatedBefore">Filter by last updated date</param>
    /// <returns>List of shipments</returns>
    Task<AmazonApiResponse<List<GetShipmentResponse>>> GetInboundShipmentsAsync(
        List<string>? shipmentStatusList = null,
        List<string>? shipmentIdList = null,
        DateTime? lastUpdatedAfter = null,
        DateTime? lastUpdatedBefore = null);

    /// <summary>
    /// Generate transportation options using v2024-03-20 API
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <returns>Transportation options response</returns>
    Task<AmazonApiResponse<object>> GenerateTransportationOptionsAsync(string inboundPlanId);

    /// <summary>
    /// List available transportation options using v2024-03-20 API
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <returns>Transportation options list</returns>
    Task<AmazonApiResponse<object>> ListTransportationOptionsAsync(string inboundPlanId);

    /// <summary>
    /// Confirm transportation options using v2024-03-20 API
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <param name="transportationOptionId">Transportation option ID</param>
    /// <returns>Confirmation response</returns>
    Task<AmazonApiResponse<object>> ConfirmTransportationOptionsAsync(string inboundPlanId, string transportationOptionId);

    /// <summary>
    /// Gets labels for a shipment (preserved from v0 API)
    /// </summary>
    /// <param name="shipmentId">Shipment ID</param>
    /// <param name="pageType">Page type (PackageLabel_Letter_6, etc.)</param>
    /// <param name="labelType">Label type (UNIQUE, PALLET)</param>
    /// <param name="numberOfPackages">Number of packages</param>
    /// <returns>Labels response</returns>
    Task<AmazonApiResponse<object>> GetLabelsAsync(
        string shipmentId,
        string pageType = "PackageLabel_Letter_6",
        string labelType = "UNIQUE",
        int? numberOfPackages = null);

    /// <summary>
    /// Gets bill of lading for LTL shipments (preserved from v0 API)
    /// </summary>
    /// <param name="shipmentId">Shipment ID</param>
    /// <returns>Bill of lading</returns>
    Task<AmazonApiResponse<object>> GetBillOfLadingAsync(string shipmentId);

    /// <summary>
    /// Validates credentials and connectivity
    /// </summary>
    /// <returns>True if valid</returns>
    Task<bool> ValidateCredentialsAsync();

    /// <summary>
    /// Generate placement options for destination fulfillment centers
    /// Required for India marketplace workflow (Step 2)
    /// For India marketplace, this is where we specify the destination fulfillment center and items
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <param name="destinationFulfillmentCenterId">Destination fulfillment center ID for India marketplace</param>
    /// <param name="items">Items for placement (required for India marketplace)</param>
    /// <returns>Placement options response</returns>
    Task<AmazonApiResponse<object>> GeneratePlacementOptionsAsync(string inboundPlanId, string? destinationFulfillmentCenterId = null, List<ShipmentItem>? items = null);

    /// <summary>
    /// List placement options for destination fulfillment centers
    /// Required for India marketplace workflow (Step 2)
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <param name="pageSize">Number of placement options to return (1-20, defaults to 1)</param>
    /// <param name="nextToken">Token for pagination (defaults to "1")</param>
    /// <returns>Placement options list</returns>
    Task<AmazonApiResponse<PlacementOptionsResponse>> ListPlacementOptionsAsync(string inboundPlanId, int pageSize = 1, string? nextToken = "1");

    /// <summary>
    /// Set packing information for shipments
    /// Required for India marketplace workflow (Step 3)
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <param name="packingInformation">Packing details including box dimensions and weights</param>
    /// <returns>Packing information response</returns>
    Task<AmazonApiResponse<object>> SetPackingInformationAsync(string inboundPlanId, object packingInformation);

    /// <summary>
    /// Confirm placement options for an inbound plan
    /// Required for India marketplace workflow (Step 4)
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <param name="placementOptionId">Selected placement option ID</param>
    /// <returns>Placement confirmation response</returns>
    Task<AmazonApiResponse<object>> ConfirmPlacementOptionForIndiaPlanAsync(string inboundPlanId, string placementOptionId);

    /// <summary>
    /// Get shipment details using v2024-03-20 API
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <param name="shipmentId">Shipment ID</param>
    /// <returns>Shipment details</returns>
    Task<AmazonApiResponse<object>> GetShipmentDetailsAsync(string inboundPlanId, string shipmentId);

    /// <summary>
    /// Generate self-ship appointment slots (Step 7 - self-ship only)
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <param name="shipmentId">Shipment ID</param>
    /// <param name="startDate">Start date for appointment slots</param>
    /// <param name="endDate">End date for appointment slots</param>
    /// <returns>Appointment slots response</returns>
    Task<AmazonApiResponse<object>> GenerateSelfShipAppointmentSlotsAsync(string inboundPlanId, string shipmentId, DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// Get available self-ship appointment slots (Step 8 - self-ship only)
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <param name="shipmentId">Shipment ID</param>
    /// <returns>Available appointment slots</returns>
    Task<AmazonApiResponse<object>> GetSelfShipAppointmentSlotsAsync(string inboundPlanId, string shipmentId);

    /// <summary>
    /// Schedule a self-ship appointment slot (Step 8 - self-ship only)
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <param name="shipmentId">Shipment ID</param>
    /// <param name="slotId">Slot ID to schedule</param>
    /// <param name="reasonComment">Reason comment (required for rescheduling)</param>
    /// <returns>Schedule appointment response</returns>
    Task<AmazonApiResponse<object>> ScheduleSelfShipAppointmentAsync(string inboundPlanId, string shipmentId, string slotId, string? reasonComment = null);

    /// <summary>
    /// Cancel a self-ship appointment slot
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <param name="shipmentId">Shipment ID</param>
    /// <returns>Cancel appointment response</returns>
    Task<AmazonApiResponse<object>> CancelSelfShipAppointmentAsync(string inboundPlanId, string shipmentId);

    /// <summary>
    /// Get inbound operation status (used to check async operation status)
    /// </summary>
    /// <param name="operationId">Operation ID from async operations</param>
    /// <returns>Operation status</returns>
    Task<AmazonApiResponse<OperationStatusResponse>> GetInboundOperationStatusAsync(string operationId);

    /// <summary>
    /// Polls operation status until completion or timeout
    /// CRITICAL: Implements proper while loop polling as per Amazon API requirements
    /// </summary>
    Task<(bool Success, string? ErrorMessage)> PollOperationStatusAsync(
        string operationId,
        string operationName,
        int maxStatusChecks = 15,
        int statusCheckDelayMs = 10000);

    /// <summary>
    /// Get delivery challan document (India marketplace specific)
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <param name="shipmentId">Shipment ID</param>
    /// <returns>Delivery challan document</returns>
    Task<AmazonApiResponse<object>> GetDeliveryChallanDocumentAsync(string inboundPlanId, string shipmentId);
}

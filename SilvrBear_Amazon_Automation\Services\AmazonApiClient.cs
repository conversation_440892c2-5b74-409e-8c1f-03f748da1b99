using Microsoft.Extensions.Options;
using SilvrBear_Amazon_Automation.Models;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// HTTP client wrapper for Amazon Seller API calls
/// </summary>
public class AmazonApiClient
{
    private readonly HttpClient _httpClient;
    private readonly AuthenticationService _authService;
    private readonly AmazonCredentials _credentials;
    private readonly ILogger<AmazonApiClient> _logger;
    private readonly JsonSerializerOptions _jsonOptions;
    private readonly Dictionary<string, DateTime> _lastRequestTimes = new();

    public AmazonApiClient(
        HttpClient httpClient,
        AuthenticationService authService,
        IOptions<AmazonCredentials> credentials,
        ILogger<AmazonApiClient> logger)
    {
        _httpClient = httpClient;
        _authService = authService;
        _credentials = credentials.Value;
        _logger = logger;
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true,
            PropertyNameCaseInsensitive = true,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };

        // Configure HttpClient base address
        _httpClient.BaseAddress = new Uri(_credentials.BaseUrl);
    }

    /// <summary>
    /// Makes a GET request to the Amazon API
    /// </summary>
    /// <typeparam name="T">Response type</typeparam>
    /// <param name="endpoint">API endpoint</param>
    /// <param name="queryParameters">Query parameters</param>
    /// <returns>API response</returns>
    public async Task<AmazonApiResponse<T>> GetAsync<T>(string endpoint, Dictionary<string, string>? queryParameters = null)
    {
        try
        {
            var url = BuildUrl(endpoint, queryParameters);
            var headers = await _authService.GetAuthorizationHeadersAsync();

            var request = new HttpRequestMessage(HttpMethod.Get, url);
            AddHeaders(request, headers);

            // Debug logging for request details
            _logger.LogInformation("=== GET REQUEST DEBUG ===");
            _logger.LogInformation("Endpoint: {Endpoint}", endpoint);
            _logger.LogInformation("Full URL: {Url}", url);
            _logger.LogInformation("Query Parameters: {QueryParams}",
                queryParameters != null ? JsonSerializer.Serialize(queryParameters, _jsonOptions) : "None");
            _logger.LogInformation("Request Headers: {Headers}",
                string.Join(", ", request.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));

            var response = await _httpClient.SendAsync(request);
            return await ProcessResponse<T>(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error making GET request to {Endpoint}", endpoint);
            return CreateErrorResponse<T>(ex.Message);
        }
    }

    /// <summary>
    /// Makes a GET request to the Amazon API for endpoints that return data directly (not wrapped in payload)
    /// Used specifically for operation status endpoints that don't follow the standard Amazon API response format
    /// </summary>
    /// <typeparam name="T">Response type</typeparam>
    /// <param name="endpoint">API endpoint</param>
    /// <param name="queryParameters">Query parameters</param>
    /// <returns>API response with direct deserialization</returns>
    public async Task<AmazonApiResponse<T>> GetDirectAsync<T>(string endpoint, Dictionary<string, string>? queryParameters = null)
    {
        try
        {
            var url = BuildUrl(endpoint, queryParameters);
            var headers = await _authService.GetAuthorizationHeadersAsync();

            var request = new HttpRequestMessage(HttpMethod.Get, url);
            AddHeaders(request, headers);

            // Debug logging for request details
            _logger.LogInformation("=== GET REQUEST DEBUG ===");
            _logger.LogInformation("Endpoint: {Endpoint}", endpoint);
            _logger.LogInformation("Full URL: {Url}", url);
            _logger.LogInformation("Query Parameters: {QueryParams}",
                queryParameters != null ? JsonSerializer.Serialize(queryParameters, _jsonOptions) : "None");
            _logger.LogInformation("Request Headers: {Headers}",
                string.Join(", ", request.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));

            var response = await _httpClient.SendAsync(request);
            return await ProcessDirectResponse<T>(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error making GET direct request to {Endpoint}", endpoint);
            return CreateErrorResponse<T>(ex.Message);
        }
    }

    /// <summary>
    /// Makes a POST request to the Amazon API
    /// </summary>
    /// <typeparam name="T">Response type</typeparam>
    /// <param name="endpoint">API endpoint</param>
    /// <param name="payload">Request payload</param>
    /// <returns>API response</returns>
    public async Task<AmazonApiResponse<T>> PostAsync<T>(string endpoint, object? payload = null)
    {
        const int maxRetries = 3;
        const int baseDelayMs = 1000;

        for (int attempt = 0; attempt <= maxRetries; attempt++)
        {
            try
            {
                // Add exponential backoff delay for retries
                if (attempt > 0)
                {
                    var delay = baseDelayMs * Math.Pow(2, attempt - 1);
                    _logger.LogInformation("Retry attempt {Attempt}/{MaxRetries} for {Endpoint}, waiting {Delay}ms",
                        attempt, maxRetries, endpoint, delay);
                    await Task.Delay((int)delay);
                }

                // Throttle requests to same endpoint to avoid rapid successive calls
                await ThrottleRequest(endpoint);

                var headers = await _authService.GetAuthorizationHeadersAsync();
                var request = new HttpRequestMessage(HttpMethod.Post, endpoint);

                string? jsonPayload = null;
                if (payload != null)
                {
                    jsonPayload = JsonSerializer.Serialize(payload, _jsonOptions);
                    request.Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
                }

                AddHeaders(request, headers);

                // Debug logging for request details
                _logger.LogInformation("=== POST REQUEST DEBUG ===");
                _logger.LogInformation("Endpoint: {Endpoint}", endpoint);
                _logger.LogInformation("Request Headers: {Headers}",
                    string.Join(", ", request.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));
                _logger.LogInformation("Request Payload: {Payload}", jsonPayload ?? "None");

                var response = await _httpClient.SendAsync(request);
                var result = await ProcessResponse<T>(response);

                // If successful or non-retryable error, return result
                if (result.IsSuccess || !IsRetryableError(response.StatusCode))
                {
                    return result;
                }

                // Log retry-worthy error
                _logger.LogWarning("Retryable error on attempt {Attempt}: {StatusCode}",
                    attempt + 1, response.StatusCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error making POST request to {Endpoint} on attempt {Attempt}", endpoint, attempt + 1);

                // If this is the last attempt, return error
                if (attempt == maxRetries)
                {
                    return CreateErrorResponse<T>(ex.Message);
                }
            }
        }

        // This should never be reached, but just in case
        return CreateErrorResponse<T>("All retry attempts failed");
    }

    /// <summary>
    /// Determines if an HTTP status code indicates a retryable error
    /// </summary>
    private static bool IsRetryableError(HttpStatusCode statusCode)
    {
        return statusCode switch
        {
            HttpStatusCode.InternalServerError => true,      // 500
            HttpStatusCode.BadGateway => true,               // 502
            HttpStatusCode.ServiceUnavailable => true,      // 503
            HttpStatusCode.GatewayTimeout => true,          // 504
            HttpStatusCode.TooManyRequests => true,         // 429
            HttpStatusCode.RequestTimeout => true,          // 408
            _ => false
        };
    }

    /// <summary>
    /// Throttles requests to prevent rapid successive calls to the same endpoint
    /// </summary>
    private async Task ThrottleRequest(string endpoint)
    {
        const int minIntervalMs = 1000; // Minimum 1 second between requests to same endpoint

        if (_lastRequestTimes.TryGetValue(endpoint, out var lastRequestTime))
        {
            var timeSinceLastRequest = DateTime.UtcNow - lastRequestTime;
            if (timeSinceLastRequest.TotalMilliseconds < minIntervalMs)
            {
                var waitTime = minIntervalMs - (int)timeSinceLastRequest.TotalMilliseconds;
                _logger.LogInformation("Throttling request to {Endpoint}, waiting {WaitTime}ms", endpoint, waitTime);
                await Task.Delay(waitTime);
            }
        }

        _lastRequestTimes[endpoint] = DateTime.UtcNow;
    }

    /// <summary>
    /// Makes a POST request that returns a direct response (not wrapped in payload)
    /// Used for v2024-03-20 API endpoints that return direct objects
    /// </summary>
    /// <typeparam name="T">Response type</typeparam>
    /// <param name="endpoint">API endpoint</param>
    /// <param name="payload">Request payload</param>
    /// <returns>API response</returns>
    public async Task<AmazonApiResponse<T>> PostDirectAsync<T>(string endpoint, object? payload = null)
    {
        try
        {
            var headers = await _authService.GetAuthorizationHeadersAsync();
            var request = new HttpRequestMessage(HttpMethod.Post, endpoint);
            
            string? jsonPayload = null;
            if (payload != null)
            {
                jsonPayload = JsonSerializer.Serialize(payload, _jsonOptions);
                request.Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
            }

            AddHeaders(request, headers);

            // Debug logging for request details
            _logger.LogInformation("=== POST DIRECT REQUEST DEBUG ===");
            _logger.LogInformation("Endpoint: {Endpoint}", endpoint);
            _logger.LogInformation("Request Headers: {Headers}", 
                string.Join(", ", request.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));
            _logger.LogInformation("Request Payload: {Payload}", jsonPayload ?? "None");

            var response = await _httpClient.SendAsync(request);
            return await ProcessDirectResponse<T>(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error making POST request to {Endpoint}", endpoint);
            return CreateErrorResponse<T>(ex.Message);
        }
    }

    /// <summary>
    /// Makes a PUT request to the Amazon API
    /// </summary>
    /// <typeparam name="T">Response type</typeparam>
    /// <param name="endpoint">API endpoint</param>
    /// <param name="payload">Request payload</param>
    /// <returns>API response</returns>
    public async Task<AmazonApiResponse<T>> PutAsync<T>(string endpoint, object? payload = null)
    {
        try
        {
            var headers = await _authService.GetAuthorizationHeadersAsync();
            var request = new HttpRequestMessage(HttpMethod.Put, endpoint);
            
            string? jsonPayload = null;
            if (payload != null)
            {
                jsonPayload = JsonSerializer.Serialize(payload, _jsonOptions);
                request.Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
            }

            AddHeaders(request, headers);

            // Debug logging for request details
            _logger.LogInformation("=== PUT REQUEST DEBUG ===");
            _logger.LogInformation("Endpoint: {Endpoint}", endpoint);
            _logger.LogInformation("Request Headers: {Headers}", 
                string.Join(", ", request.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));
            _logger.LogInformation("Request Payload: {Payload}", jsonPayload ?? "None");

            var response = await _httpClient.SendAsync(request);
            return await ProcessResponse<T>(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error making PUT request to {Endpoint}", endpoint);
            return CreateErrorResponse<T>(ex.Message);
        }
    }

    /// <summary>
    /// Builds URL with query parameters
    /// </summary>
    private static string BuildUrl(string endpoint, Dictionary<string, string>? queryParameters)
    {
        if (queryParameters == null || queryParameters.Count == 0)
            return endpoint;

        var queryString = string.Join("&", queryParameters.Select(kvp => $"{kvp.Key}={Uri.EscapeDataString(kvp.Value)}"));
        return $"{endpoint}?{queryString}";
    }

    /// <summary>
    /// Adds headers to the request
    /// </summary>
    private static void AddHeaders(HttpRequestMessage request, Dictionary<string, string> headers)
    {
        foreach (var header in headers)
        {
            if (header.Key.Equals("Content-Type", StringComparison.OrdinalIgnoreCase))
                continue; // Content-Type is handled by StringContent

            request.Headers.TryAddWithoutValidation(header.Key, header.Value);
        }
    }

    /// <summary>
    /// Processes the HTTP response for direct API responses (not wrapped in payload)
    /// </summary>
    private async Task<AmazonApiResponse<T>> ProcessDirectResponse<T>(HttpResponseMessage response)
    {
        var content = await response.Content.ReadAsStringAsync();
        
        // Debug logging for response details
        _logger.LogInformation("=== API RESPONSE DEBUG (DIRECT) ===");
        _logger.LogInformation("Response Status Code: {StatusCode}", response.StatusCode);
        _logger.LogInformation("Response Headers: {Headers}", 
            string.Join(", ", response.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));
        _logger.LogInformation("Response Content: {Content}", content);
        _logger.LogInformation("=== END RESPONSE DEBUG ===");

        if (!response.IsSuccessStatusCode)
        {
            _logger.LogWarning("API request failed with status {StatusCode}: {Content}", response.StatusCode, content);

            // Try to parse error response
            try
            {
                var errorResponse = JsonSerializer.Deserialize<AmazonApiResponse<T>>(content, _jsonOptions);
                if (errorResponse != null)
                {
                    // Enhance error messages for v2024-03-20 API specific errors
                    foreach (var error in errorResponse.Errors)
                    {
                        error.Message = EnhanceErrorMessage(error.Code, error.Message, response.StatusCode);
                    }
                    return errorResponse;
                }
            }
            catch (JsonException)
            {
                // If we can't parse the error response, create a generic error
            }

            return CreateErrorResponse<T>(EnhanceErrorMessage("HTTP_ERROR", content, response.StatusCode));
        }

        try
        {
            _logger.LogInformation("Attempting to deserialize direct response to type: {Type}", typeof(T).Name);
            // For direct responses, deserialize directly to T, then wrap in AmazonApiResponse
            var directResult = JsonSerializer.Deserialize<T>(content, _jsonOptions);
            return new AmazonApiResponse<T>
            {
                Payload = directResult,
                Errors = new List<ApiError>()
            };
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Failed to deserialize direct response: {Content}", content);
            return CreateErrorResponse<T>($"Deserialization error: {ex.Message}");
        }
    }

    /// <summary>
    /// Processes the HTTP response
    /// </summary>
    private async Task<AmazonApiResponse<T>> ProcessResponse<T>(HttpResponseMessage response)
    {
        var content = await response.Content.ReadAsStringAsync();
        
        // Debug logging for response details
        _logger.LogInformation("=== API RESPONSE DEBUG ===");
        _logger.LogInformation("Response Status Code: {StatusCode}", response.StatusCode);
        _logger.LogInformation("Response Headers: {Headers}", 
            string.Join(", ", response.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));
        _logger.LogInformation("Response Content: {Content}", content);
        _logger.LogInformation("=== END RESPONSE DEBUG ===");

        if (!response.IsSuccessStatusCode)
        {
            _logger.LogWarning("API request failed with status {StatusCode}: {Content}", response.StatusCode, content);

            // Try to parse error response
            try
            {
                var errorResponse = JsonSerializer.Deserialize<AmazonApiResponse<T>>(content, _jsonOptions);
                if (errorResponse != null)
                {
                    // Enhance error messages for v2024-03-20 API specific errors
                    foreach (var error in errorResponse.Errors)
                    {
                        error.Message = EnhanceErrorMessage(error.Code, error.Message, response.StatusCode);
                    }
                    return errorResponse;
                }
            }
            catch (JsonException)
            {
                // If we can't parse the error response, create a generic error
            }

            return CreateErrorResponse<T>(EnhanceErrorMessage("HTTP_ERROR", content, response.StatusCode));
        }

        try
        {
            _logger.LogInformation("Attempting to deserialize response to type: {Type}", typeof(T).Name);
            
            // Enhanced deserialization debugging
            _logger.LogInformation("JSON content length: {Length} characters", content.Length);
            _logger.LogInformation("JSON content preview: {Preview}", content.Length > 500 ? content.Substring(0, 500) + "..." : content);
            
            var apiResponse = JsonSerializer.Deserialize<AmazonApiResponse<T>>(content, _jsonOptions);
            
            // Enhanced payload debugging
            if (apiResponse != null)
            {
                _logger.LogInformation("Deserialization successful - ApiResponse is not null");
                _logger.LogInformation("ApiResponse.Payload is null: {IsNull}", apiResponse.Payload == null);
                _logger.LogInformation("ApiResponse.Errors count: {ErrorCount}", apiResponse.Errors?.Count ?? 0);
                
                if (apiResponse.Payload != null)
                {
                    _logger.LogInformation("Payload type: {PayloadType}", apiResponse.Payload.GetType().Name);
                    _logger.LogInformation("Payload content: {PayloadContent}", JsonSerializer.Serialize(apiResponse.Payload, _jsonOptions));
                }
            }
            else
            {
                _logger.LogWarning("Deserialization returned null ApiResponse");
            }
            
            return apiResponse ?? CreateErrorResponse<T>("Failed to deserialize response");
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Failed to deserialize response: {Content}", content);
            return CreateErrorResponse<T>($"Deserialization error: {ex.Message}");
        }
    }

    /// <summary>
    /// Creates an error response
    /// </summary>
    private static AmazonApiResponse<T> CreateErrorResponse<T>(string message)
    {
        return new AmazonApiResponse<T>
        {
            Errors = new List<ApiError>
            {
                new ApiError
                {
                    Code = "CLIENT_ERROR",
                    Message = message
                }
            }
        };
    }

    /// <summary>
    /// Enhances error messages for better user understanding, especially for v2024-03-20 API
    /// </summary>
    private static string EnhanceErrorMessage(string errorCode, string originalMessage, System.Net.HttpStatusCode statusCode)
    {
        return errorCode.ToUpper() switch
        {
            "INVALID_ARGUMENT" => "Invalid request parameters. Please check your input data and try again.",
            "RESOURCE_NOT_FOUND" => "The requested resource was not found. Please verify the ID and try again.",
            "PERMISSION_DENIED" => "Access denied. Please check your API permissions and credentials.",
            "QUOTA_EXCEEDED" => "API rate limit exceeded. Please wait before making more requests.",
            "INTERNAL_SERVER_ERROR" or "INTERNALSERVERERROR" => "Amazon's servers are experiencing issues. Please try again later.",
            "INVALID_ACCESS_TOKEN" => "Your access token is invalid or expired. Please refresh your credentials.",
            "UNAUTHORIZED" => "Authentication failed. Please check your API credentials.",
            "DEPRECATED_API" => "This API version is deprecated. Please migrate to the latest version.",
            "HTTP_ERROR" when statusCode == System.Net.HttpStatusCode.BadRequest =>
                "Bad request. Please check your request format and parameters.",
            "HTTP_ERROR" when statusCode == System.Net.HttpStatusCode.Unauthorized =>
                "Authentication failed. Please check your API credentials.",
            "HTTP_ERROR" when statusCode == System.Net.HttpStatusCode.Forbidden =>
                "Access forbidden. You don't have permission to access this resource.",
            "HTTP_ERROR" when statusCode == System.Net.HttpStatusCode.NotFound =>
                "Resource not found. Please verify the endpoint and resource ID.",
            "HTTP_ERROR" when statusCode == System.Net.HttpStatusCode.TooManyRequests =>
                "Too many requests. Please wait before making more API calls.",
            "HTTP_ERROR" when statusCode == System.Net.HttpStatusCode.InternalServerError =>
                "Amazon's servers are experiencing issues. Please try again later.",
            _ => string.IsNullOrEmpty(originalMessage) ? $"API Error ({errorCode})" : originalMessage
        };
    }
}
